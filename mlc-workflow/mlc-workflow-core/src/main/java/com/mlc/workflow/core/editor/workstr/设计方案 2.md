# 大纲

1. 术语与数据模型统一
   1.1 节点类型、网关类型（并行/唯一）
   1.2 结束哨兵（`nextId=99`）与分支尾部空指针（`nextId=""`）约束
   1.3 子流程语义与边界

2. 遍历引擎（非 DAG）的基线策略
   2.1 深度优先（分支顺序可控）遍历
   2.2 遍历光标（Cursor）+ 上下文栈（父网关/分支位置信息）

3. 网关操作（新增/删除/类型切换）及副作用
   3.1 新增网关：
   \- “左侧放置”与“不移动”两种策略
   \- 默认分支与分支尾部连线策略
   3.2 删除网关：
   \- 删除分支后仅剩一条分支时的折叠规则
   3.3 网关类型切换：并行 ⇄ 唯一（条件集管理、非法迁移防护）

4. 分支操作（增/删/调序/复制）及副作用
   4.1 唯一网关分支的条件完整性
   4.2 并行网关分支的独立性与汇聚一致性
   4.3 复制分支的 ID、条件、子图去重策略

5. 普通节点 CRUD
   5.1 插入（前/后/包裹）
   5.2 删除（含分支尾、子流程入口/出口）
   5.3 修改（结构性与非结构性属性的区分）

6. 通用“上下文自动连线”策略（贯穿所有 CRUD）
   6.1 断开 / 连接 / 替换 / 拼接片段
   6.2 汇聚点与结束哨兵的一致性维护
   6.3 主/子流程的 `99` 唯一性守护

7. 规则与校验（提交前统一验证）
   7.1 `nextId=99` 唯一性（主/各子流程各自仅能存在一次）
   7.2 网关/分支基本不变量（分支数、条件覆盖、尾部连线……）
   7.3 没有孤儿节点/断链、没有死循环
   7.4 子流程边界：入口/出口、哨兵隔离

8. 设计模式与组件分层
   8.1 结构层：Composite + Strategy + Visitor
   8.2 操作层：Application Service（可选轻量 Command，无撤销）
   8.3 校验层：Policy/Specification + Validator
   8.4 基础设施：Repository（JSON 映射）、ID 生成、Unit of Work

9. 典型用例步进（依据 `workFlow.txt`）
   9.1 在主流程某节点上“左侧放置”新增网关
   9.2 唯一网关删除分支导致仅剩 1 条分支 → 网关折叠
   9.3 分支复制并调整顺序
   9.4 子流程中维护 `99` 唯一性

10. 渐进式落地建议（可测试/回归/灰度与日志）

---

# 可落地的实现方案（含设计模式）

## 1) 术语与数据模型统一

* **IRoutable**：具备路由能力，至少暴露 `getNextId()/setNextId()`（或等价）用于自动连线。

* **IHasBranches**：代表“网关”持有分支集合（如 `flowIds: List<String>`），每个分支指向一个分支头节点（通常是普通节点或分支子图的第一个节点）。

* **IHasConditions**：仅对**唯一分支**（XOR）网关要求，分支具备条件；对**并行分支**（AND）网关，条件应为空或忽略。

* **IHasSubProcess**：节点能持有 `ProcessNode` 子流程。

* **ProcessNode**：流程聚合根（主/子）。包含 `startEventId`、`flowNodeMap`、主/子流程标记等。

* **结束哨兵**：`nextId=99` 代表“连接至结束节点”。**约束**：在**每个**（主/子）流程内仅能出现**一次**；主流程可 1 个、每个子流程可各有 1 个。

* **分支尾部空指针**：`nextId=""` 表示“该分支在网关汇聚前**没有**后继节点”，由网关的汇聚逻辑统一决定走向。

* **（经验规则）**：常见模式是在唯一/并行网关下，分支**尾**用 `""`，网关汇聚后再接主线；但也允许**某些分支**提前直接 `99`（等价“短路到结束”），此时必须守住该流程的 `99` 唯一性（样例中一条分支 `nextId=99`、另一条 `nextId=""` 即属此类）。

> 说明：以上语义与 `workFlow.txt` 的结构表现保持一致（如 `typeId=1` 的“分支”节点、两条分支 `flowIds`、其中一条分支尾 `nextId=99`、另一条 `nextId=""`，以及子流程的 `processNode`）。

## 2) 遍历引擎（非 DAG）

**目标**：在“非 DAG”设定下，采用**深度优先**遍历（DFS）+ **上下文栈**维护父网关与分支位置信息，统一驱动**查找/定位/CRUD/校验**。

* **Visitor 模式**（结构型 + 行为型）：

  * 定义 `WorkflowVisitor`，对不同节点类型（普通、网关、子流程入口/出口）提供 `visitXxx`。
  * 通过 Visitor 收集位置上下文（当前所在网关、分支序号、上一可路由节点 `IRoutable` 等）。

* **TraversalCursor（遍历光标）**：

  * `{ currentId, parentGatewayId, branchIndex, prevRoutableId }`
  * 为**插入、删除、替换**等操作提供定位能力，避免硬拼 `prveId`/`nextId`。

* **顺序**：

  * 访问普通节点 → 按 `nextId` 前进；
  * 访问网关（`IHasBranches`）：按**当前网关的分支顺序**遍历每条分支子链；完成后进入**汇聚后**的后继（如果存在）；
  * 访问子流程：切入其 `ProcessNode`（独立的 99 约束空间）。

> 备注：遍历实现中务必防止环链（提交前统一校验）。

## 3) 网关操作（新增/删除/类型切换）

### 3.1 新增网关

* **适用位置**：在遍历光标指向的某个普通节点（或可路由节点）**之后**插入一个网关。

* **两种放置策略**（满足你的 3.1.1）：

  1. **左侧放置**：

    * **语义**：把**当前节点之后**的所有“下方”节点整体移动到**默认两条分支中的左侧分支**上；右侧分支保持为空。
    * **默认尾部连线**：

      * **基础（严格）模式**：为满足“每个流程仅一个 `99`”约束，**左分支尾 → `99`**，**右分支尾 → `""`**；网关汇聚后**无后继**（或由后续再接）。
      * **业务（外观）模式**：若产品期望“两条默认分支都直接连到结束节点”，则引入\*\*“虚拟汇聚占位”**：两分支尾 → 占位；占位 → `99`。这样**物理上仅产生 1 个 `99`\*\*，外观仍像“两分支都到达结束”。
    * **副作用处理**：移动子图后，需要**修复**被移动前驱的 `nextId` → 指向新网关；并**清空**被移动片段最后一节点的原 `nextId`（改为 `""` 或由自动连线策略接管）。
  2. **不移动**：

    * **语义**：只在当前位置插入一个空网关，**不触碰**后续节点；两条默认分支为空，分支尾均 `""`，等待后续填充并在网关汇聚后再接后继。
    * **副作用处理**：将“插入点”的前后关系改为：前驱 → 新网关；新网关的**汇聚后继** → 原后继。

* **模式选型**：

  * **Builder + Factory**：`GatewayBuilder` 快速构建默认并行网关（两条分支、条件为空）。
  * **Strategy（AutoWire）**：选择严格模式或外观模式的“默认尾部连线策略”。
  * **Unit of Work**：插入-移动-修复-校验打包为一个事务性操作。

### 3.2 删除网关

* **一般删除**：

  * 并行网关删除：可将多条分支**顺序拼接**或选择保留一条为主链、其余“溶解”入后继，依据策略配置（推荐用户选择）。
  * 唯一网关删除：需要重分配各分支的结构（通常不建议直接删除，除非只剩一条分支）。

* **特例（你的 3.1.2）**：**当删除分支操作后仅剩 1 条分支**：

  * **动作**：**彻底删除**网关与条件分支容器；
  * **连线**：将这**最后一条分支的节点**视为**普通链**，执行**上下文自动连线**：前驱 → 该分支首节点；该分支尾 → 原网关的汇聚后继（或 `99`）。
  * **校验**：若该唯一分支内尾部曾直连 `99`，需确认该流程内不会出现**第二个** `99`。

* **模式选型**：

  * **Composite**：把“网关 + 分支子图”当作可整体收缩/展开的复合节点。
  * **Visitor**：定位最后留存的分支首尾以自动连线。
  * **Specification**：保证删除后的链仍满足 `99` 唯一性与无断链。

### 3.3 修改网关类型（并行 ⇄ 唯一）

* **并行 → 唯一**：

  * 要求**每条分支具备互斥条件**（`IHasConditions`）；若缺失，阻止提交，并提供“自动生成兜底条件”的向导。
  * 若原分支尾有 `99`，需确认转为唯一后仍满足唯一性与可达性。

* **唯一 → 并行**：

  * 清理条件集合或标记为忽略；
  * 若存在“仅一条分支 `99`”的短路设定，保持不变，其他分支尾部使用 `""`。

* **模式选型**：

  * **Policy/Validator**：类型切换前后的结构合法性验证。
  * **Strategy**：不同类型的条件处理与尾部连线策略切换。

## 4) 分支操作（增/删/调序/复制）

* **增加分支**：

  * 并行网关：直接新增空分支，尾 `""`。
  * 唯一网关：新增后需**校验条件覆盖**（可提供“复制已有条件+微调”的引导）。

* **删除分支**：

  * 删除后检查“网关分支数是否 < 2”：若为 1，触发**网关折叠**（见 3.2 特例）。
  * 若被删分支尾接 `99`，需**迁移**该 `99` 指向到网关汇聚或其他合法位置，确保唯一性。

* **调整分支顺序**：

  * 并行网关：仅影响**展示顺序**；
  * 唯一网关：**影响判定优先级**，必须触发条件覆盖校验（首命中原则）。

* **复制分支**：

  * 复制子图（含子流程、节点属性与条件）；
  * **ID 重映射**（避免与原子图冲突）；
  * 唯一网关复制时**不得复制出第二个 `99`**，如遇 `99`，复制侧尾部一律改为 `""` 并弹提示。

* **模式选型**：

  * **Prototype**（浅/深复制策略）
  * **Strategy（IDRemap）**：统一处理 ID 重映射与 `nextId` 修复。
  * **Specification**：复制后的一致性检查。

## 5) 普通节点 CRUD

* **增加**：

  * `insertAfter(prev, newNode)`：自动连线 `prev → newNode → oldNext`；若 `prev.nextId=99`，需将 `prev.nextId` 改接 `newNode`，并把 `newNode.nextId=99`，保持流程 `99` 唯一。
  * 在**分支尾**插入：若尾本为 `""`，插入后尾接新节点，新节点尾仍可 `""` 或接网关汇聚，按策略。

* **删除**：

  * 普通链：`prev.nextId = curr.nextId`，并清理 `curr`。
  * 分支尾节点：删除后若分支为空，视为**空分支**（可触发后续折叠/删除分支）。
  * 若删的是**唯一承载 `99` 的节点**，需将 `99` 迁移到**下一个合适位置**（通常是网关汇聚或主线尾）。

* **修改**：

  * 非结构性（名称、账号、表单属性…）不触碰连线；
  * 结构性（`nextId`、`selectNodeId` 等）必须走**自动连线策略**与**校验**。

* **模式选型**：

  * **Template Method**：不同上下文（主线/分支/子流程）复用 CRUD 模板步骤，具体差异在钩子中实现。
  * **Strategy（AutoWire）**：统一连线。

## 6) 通用“上下文自动连线”策略（核心）

* 定义 `AutoWireStrategy`（策略模式），提供：

  * `disconnect(A→B)`：断开并维护 `prveId/nextId` 的一致性；
  * `connect(A→B)`：连接并回填双向引用（若数据模型仅存 `nextId`，则维护“反向索引”供遍历期临时使用）；
  * `replace(A→X→B)`：原子地替换中间节点/子图；
  * `splice([A..Z] → target)`：片段拼接。

* **汇聚与结束哨兵**：

  * 流程内**仅一个 `99`**（主/子流程各自计算）；
  * 若一次操作产生第二个 `99`，策略做**唯一化修正**：

    1. 优先让“更接近尾部”的那条路径保留 `99`；
    2. 另一处改为 `""` 或接汇聚；
    3. 如业务强制“两分支直接到结束”，启用**虚拟汇聚占位**（占位→`99`）。

* **子流程隔离**：

  * 任何“跨流程”的自动连线被**禁止**；
  * 子流程内部有自己的 `99`，与主流程互不干扰（`workFlow.txt` 中的嵌套 `processNode` 即为样例）。

## 7) 规则与校验（提交前统一验证）

* **R1**：每个流程（主/子）**最多一个** `nextId=99`。

* **R2**：唯一网关（XOR）**每条分支**应具有**互斥且覆盖**的条件集（`IHasConditions`），可附加兜底分支。

* **R3**：并行网关（AND）分支不得持有条件；

* **R4**：当网关分支数 `< 2`，网关应被折叠移除（3.2 特例）。

* **R5**：不存在断链、环路、孤儿节点。

* **R6**：子流程与主流程的 `99` 各自独立计算。

* **R7**：若分支尾设置 `""`，则必须存在**汇聚后继**或上层上下文提供后继。

* **R8**：分支复制/导入不得引入第二个 `99`；若出现，必须自动修复并提示。

* **实现**：

  * **Validator（策略 + 组合）**：`EndSentinelValidator`、`GatewayValidator`、`RoutingValidator`、`SubProcessValidator`…
  * **Specification**：将复杂规则写成可组合规格，最终由 `WorkflowValidationService` 聚合执行。

## 8) 设计模式与分层

* **结构层（领域模型）**

  * **Composite**：`Node`（普通/网关/子流程）统一抽象，对网关+分支子图视为复合。
  * **Strategy**：`AutoWireStrategy`、`IdRemapStrategy`、`GatewayTypePolicy`。
  * **Visitor**：`WorkflowVisitor` 用于遍历、定位、收集结构信息。

* **应用层（操作编排）**

  * `WorkflowEditorService`：暴露高阶 API：

    * `addGateway(cursor, type, placement)`
    * `deleteGateway(gatewayId)`
    * `switchGatewayType(gatewayId, newType)`
    * `addBranch/deleteBranch/reorderBranch/copyBranch`
    * `insertNodeAfter/deleteNode/modifyNode`
  * **（可选）轻量 Command**：仅用于**原子操作打包**与日志记录（不做撤销/重做）。
  * **Unit of Work**：将一次编辑的多步改动与校验打包提交。

* **校验层**

  * **Policy + Specification + Validator** 三件套，集中在 `commit()` 或 `save()` 前运行。

* **基础设施层**

  * **Repository**：`ProcessNodeRepository` 负责把 `ProcessNode` 映射回 `workFlow.txt`/JSON（Jackson 已打通）。
  * **IdGenerator/Mapper**：分支复制、子图导入时的 ID 重映射。
  * **Audit/Log**：记录每次结构性变更（便于排障）。

## 9) 典型用例步进（结合样例结构）

> 样例里：主流程起点 → 审批（`typeId=26`）→ 网关“分支”（`typeId=1`，`flowIds` 两条）→ 一条分支尾 `nextId=99`，另一条 `nextId=""`；子流程 `processNode` 内部另有自己的起点与 `99`。

* **用例 A：在审批节点后“左侧放置”新增并行网关**

  1. `WorkflowVisitor` 找到审批节点的遍历光标；
  2. `GatewayBuilder` 构建并行网关（2 条空分支）；
  3. **左侧放置**：将审批之后的子链整体移动到**左分支**；
  4. **默认尾部连线**：严格模式 → 左分支尾 `→99`，右分支尾 `→""`；
  5. `AutoWireStrategy` 修复插入点前驱/后继；
  6. `Validator` 通过（无第二个 `99`、无断链）。

* **用例 B：唯一网关删除一条分支后仅剩 1 条**

  1. 执行 `deleteBranch`；
  2. 触发网关折叠：把剩余分支子图接回上下文（前驱 → 分支首，分支尾 → 汇聚后继）；
  3. 若尾部含 `99`，确认流程中没有另一个 `99` → 若有，则将另一处改为 `""` 并提示；
  4. 验证通过。

* **用例 C：复制分支 + 调整顺序（唯一网关）**

  1. `Prototype` 深复制子图，`IdRemapStrategy` 重映射 ID；
  2. 若原分支尾 `99`，复制分支尾改为 `""` 并提示用户调整为非终止；
  3. `reorderBranch` 调整优先级；
  4. 运行条件覆盖校验。

* **用例 D：子流程中的 `99` 唯一性**

  1. 在子流程里插入终止节点时，`EndSentinelValidator` 只在**子流程域**内统计；
  2. 与主流程互不影响，提交校验分别通过。

## 10) 渐进式落地建议

* **测试用例套件**：

  * 网关新增（左侧/不移动）×（并行/唯一）
  * 分支增删（触发折叠）、复制（含 `99` 修复）、调序（唯一网关条件覆盖）
  * 普通节点插入/删除（含原尾为 `99` 的情形）
  * 子流程内的独立校验（`99` 唯一、断链）
  * 混合场景：分支内提前 `99` 短路 + 汇聚后继接主线

* **日志与观测**：为每次结构性变更记录：操作类型、涉及节点 ID、连线前后快照、校验结果。